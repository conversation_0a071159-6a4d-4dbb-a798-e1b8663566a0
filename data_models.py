"""
数据模型定义
定义文献信息提取和存储的数据结构
"""

from dataclasses import dataclass, asdict
from typing import List, Dict, Optional, Any
import json
from datetime import datetime


@dataclass
class ResearchProblem:
    """研究问题数据结构"""
    problem_id: str  # 问题唯一标识
    title: str  # 问题标题
    description: str  # 问题描述
    keywords: List[str]  # 关键词
    domain: str  # 研究领域
    sub_domain: str  # 子领域
    research_type: str  # 研究类型（实证研究、理论研究、设计研究等）
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class ResearchMethod:
    """研究方法数据结构"""
    method_id: str  # 方法唯一标识
    name: str  # 方法名称
    description: str  # 方法描述
    category: str  # 方法类别（定量、定性、混合方法等）
    techniques: List[str]  # 具体技术手段
    data_types: List[str]  # 数据类型
    analysis_tools: List[str]  # 分析工具
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class PaperInfo:
    """论文基本信息"""
    paper_id: str  # 论文唯一标识
    title: str  # 论文标题
    authors: List[str]  # 作者列表
    year: int  # 发表年份
    venue: str  # 发表场所（期刊/会议）
    abstract: str  # 摘要
    keywords: List[str]  # 关键词
    doi: Optional[str] = None  # DOI
    url: Optional[str] = None  # 论文链接
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class ExtractedPaperData:
    """从论文中提取的完整数据"""
    paper_info: PaperInfo  # 论文基本信息
    research_problems: List[ResearchProblem]  # 研究问题列表
    research_methods: List[ResearchMethod]  # 研究方法列表
    full_text: str  # 论文全文（可选）
    extraction_date: str  # 提取日期
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'paper_info': self.paper_info.to_dict(),
            'research_problems': [p.to_dict() for p in self.research_problems],
            'research_methods': [m.to_dict() for m in self.research_methods],
            'full_text': self.full_text,
            'extraction_date': self.extraction_date
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExtractedPaperData':
        """从字典创建对象"""
        return cls(
            paper_info=PaperInfo(**data['paper_info']),
            research_problems=[ResearchProblem(**p) for p in data['research_problems']],
            research_methods=[ResearchMethod(**m) for m in data['research_methods']],
            full_text=data['full_text'],
            extraction_date=data['extraction_date']
        )


@dataclass
class SearchResult:
    """检索结果数据结构"""
    item_id: str  # 项目ID（问题ID或方法ID）
    item_type: str  # 项目类型（'problem' 或 'method'）
    content: str  # 内容文本
    score: float  # 相似度分数
    paper_id: str  # 来源论文ID
    paper_title: str  # 来源论文标题
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class DataManager:
    """数据管理器，负责数据的存储和加载"""
    
    def __init__(self, data_dir: str = "./data"):
        self.data_dir = data_dir
    
    def save_paper_data(self, paper_data: ExtractedPaperData) -> None:
        """保存论文数据"""
        filename = f"{self.data_dir}/paper_{paper_data.paper_info.paper_id}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(paper_data.to_dict(), f, ensure_ascii=False, indent=2)
    
    def load_paper_data(self, paper_id: str) -> Optional[ExtractedPaperData]:
        """加载论文数据"""
        filename = f"{self.data_dir}/paper_{paper_id}.json"
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return ExtractedPaperData.from_dict(data)
        except FileNotFoundError:
            return None
    
    def load_all_papers(self) -> List[ExtractedPaperData]:
        """加载所有论文数据"""
        import os
        papers = []
        for filename in os.listdir(self.data_dir):
            if filename.startswith('paper_') and filename.endswith('.json'):
                paper_id = filename[6:-5]  # 去掉 'paper_' 前缀和 '.json' 后缀
                paper_data = self.load_paper_data(paper_id)
                if paper_data:
                    papers.append(paper_data)
        return papers
