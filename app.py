"""
论文假设生成系统 - Streamlit前端界面
"""

import streamlit as st
import os
from dotenv import load_dotenv
import time

from data_models import DataManager
from retrieval_engine import RetrievalEngine, RetrievalConfig
from reasoning_engine import ReasoningEngine, ReasoningConfig
from semantic_space import SemanticSpaceManager

# 加载环境变量
load_dotenv()

# 页面配置
st.set_page_config(
    page_title="论文假设生成系统",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.sub-header {
    font-size: 1.5rem;
    color: #ff7f0e;
    margin-top: 2rem;
    margin-bottom: 1rem;
}
.evidence-card {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
    border-left: 4px solid #1f77b4;
}
.hypothesis-result {
    background-color: #e8f4fd;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
    border: 2px solid #1f77b4;
}
</style>
""", unsafe_allow_html=True)


@st.cache_resource
def initialize_system():
    """初始化系统组件"""
    try:
        # 检查API密钥
        if not os.getenv('OPENAI_API_KEY'):
            st.error("请设置OPENAI_API_KEY环境变量")
            return None, None
        
        # 初始化检索引擎
        retrieval_engine = RetrievalEngine()
        
        # 尝试加载已有的语义空间
        if not retrieval_engine.initialize():
            st.warning("未找到预构建的语义空间，正在构建...")
            with st.spinner("正在构建语义空间，请稍候..."):
                retrieval_engine.build_semantic_spaces()
            st.success("语义空间构建完成！")
        
        # 初始化推理引擎
        reasoning_engine = ReasoningEngine(retrieval_engine=retrieval_engine)
        
        return retrieval_engine, reasoning_engine
    
    except Exception as e:
        st.error(f"系统初始化失败：{e}")
        return None, None


def display_search_results(results, title):
    """显示搜索结果"""
    if not results:
        st.info(f"未找到相关的{title}")
        return
    
    st.markdown(f"### {title} ({len(results)}个)")
    
    for i, result in enumerate(results):
        with st.expander(f"{i+1}. {result.content[:100]}..." if len(result.content) > 100 else f"{i+1}. {result.content}"):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"**内容：** {result.content}")
                st.markdown(f"**来源论文：** {result.paper_title}")
                st.markdown(f"**类型：** {result.item_type}")
            
            with col2:
                st.metric("相关度", f"{result.score:.2f}")
                
                if st.button(f"查看详情", key=f"detail_{result.item_id}"):
                    st.session_state[f'show_detail_{result.item_id}'] = True
        
        # 显示详细信息
        if st.session_state.get(f'show_detail_{result.item_id}', False):
            paper_data = st.session_state.retrieval_engine.get_paper_content(result.paper_id)
            if paper_data:
                st.markdown("#### 论文详细信息")
                st.markdown(f"**标题：** {paper_data.paper_info.title}")
                st.markdown(f"**作者：** {', '.join(paper_data.paper_info.authors)}")
                st.markdown(f"**年份：** {paper_data.paper_info.year}")
                st.markdown(f"**摘要：** {paper_data.paper_info.abstract}")


def main():
    """主函数"""
    # 标题
    st.markdown('<h1 class="main-header">🔬 论文假设生成系统</h1>', unsafe_allow_html=True)
    st.markdown("基于学习分析领域文献的智能研究假设生成工具")
    
    # 初始化系统
    if 'retrieval_engine' not in st.session_state or 'reasoning_engine' not in st.session_state:
        retrieval_engine, reasoning_engine = initialize_system()
        if retrieval_engine and reasoning_engine:
            st.session_state.retrieval_engine = retrieval_engine
            st.session_state.reasoning_engine = reasoning_engine
        else:
            st.stop()
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 系统配置")
        
        # 检索配置
        st.subheader("检索配置")
        dense_weight = st.slider("稠密检索权重", 0.0, 1.0, 0.6, 0.1)
        sparse_weight = 1.0 - dense_weight
        st.write(f"稀疏检索权重: {sparse_weight:.1f}")
        
        top_k = st.slider("返回结果数量", 1, 20, 5)
        
        # 推理配置
        st.subheader("推理配置")
        max_iterations = st.slider("最大迭代次数", 1, 5, 3)
        temperature = st.slider("创新度", 0.0, 1.0, 0.7, 0.1)
        
        # 更新配置
        if st.button("应用配置"):
            st.session_state.retrieval_engine.config.dense_weight = dense_weight
            st.session_state.retrieval_engine.config.sparse_weight = sparse_weight
            st.session_state.retrieval_engine.config.final_top_k = top_k
            st.session_state.reasoning_engine.config.max_iterations = max_iterations
            st.session_state.reasoning_engine.config.temperature = temperature
            st.success("配置已更新！")
        
        # 系统状态
        st.subheader("系统状态")
        data_manager = DataManager()
        papers = data_manager.load_all_papers()
        st.metric("论文数量", len(papers))
        
        total_problems = sum(len(paper.research_problems) for paper in papers)
        total_methods = sum(len(paper.research_methods) for paper in papers)
        st.metric("研究问题", total_problems)
        st.metric("研究方法", total_methods)
    
    # 主界面选项卡
    tab1, tab2, tab3 = st.tabs(["💡 假设生成", "🔍 文献检索", "📊 数据管理"])
    
    with tab1:
        st.markdown('<h2 class="sub-header">研究假设生成</h2>', unsafe_allow_html=True)
        
        # 用户输入
        user_idea = st.text_area(
            "请输入您的研究想法或问题：",
            placeholder="例如：如何利用学习者的情感数据来改善在线学习体验？",
            height=100
        )
        
        col1, col2 = st.columns([1, 4])
        with col1:
            generate_button = st.button("🚀 生成假设", type="primary")
        
        if generate_button and user_idea:
            with st.spinner("正在分析文献并生成假设，请稍候..."):
                try:
                    result = st.session_state.reasoning_engine.generate_hypothesis(
                        user_idea, max_iterations
                    )
                    
                    # 显示结果
                    st.markdown('<div class="hypothesis-result">', unsafe_allow_html=True)
                    st.markdown("### 🎯 生成的研究假设")
                    st.markdown(result.hypothesis)
                    st.markdown('</div>', unsafe_allow_html=True)
                    
                    # 显示元信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("置信度", f"{result.confidence:.2f}")
                    with col2:
                        st.metric("迭代次数", result.iteration_count)
                    with col3:
                        st.metric("支持证据", len(result.supporting_evidence))
                    
                    # 显示支持证据
                    if result.supporting_evidence:
                        st.markdown("### 📚 支持证据")
                        for i, evidence in enumerate(result.supporting_evidence):
                            with st.expander(f"证据 {i+1}: {evidence.content[:80]}..."):
                                st.markdown(f"**类型：** {evidence.item_type}")
                                st.markdown(f"**内容：** {evidence.content}")
                                st.markdown(f"**来源：** {evidence.paper_title}")
                                st.markdown(f"**相关度：** {evidence.score:.2f}")
                    
                    # 显示后续问题
                    if result.follow_up_questions:
                        st.markdown("### ❓ 后续探索问题")
                        for i, question in enumerate(result.follow_up_questions):
                            st.markdown(f"{i+1}. {question}")
                    
                    # 保存结果到session state
                    st.session_state.last_hypothesis = result
                    
                except Exception as e:
                    st.error(f"生成假设时出错：{e}")
        
        # 假设完善功能
        if 'last_hypothesis' in st.session_state:
            st.markdown("---")
            st.markdown("### 🔧 完善假设")
            
            feedback = st.text_area(
                "请提供反馈或补充信息：",
                placeholder="例如：我希望重点关注移动学习环境...",
                height=80
            )
            
            if st.button("完善假设") and feedback:
                with st.spinner("正在完善假设..."):
                    try:
                        refined_result = st.session_state.reasoning_engine.refine_hypothesis(
                            st.session_state.last_hypothesis.hypothesis, feedback
                        )
                        
                        st.markdown("### 🎯 完善后的假设")
                        st.markdown(refined_result.hypothesis)
                        
                        st.session_state.last_hypothesis = refined_result
                        
                    except Exception as e:
                        st.error(f"完善假设时出错：{e}")
    
    with tab2:
        st.markdown('<h2 class="sub-header">文献检索</h2>', unsafe_allow_html=True)
        
        # 搜索输入
        search_query = st.text_input(
            "搜索关键词：",
            placeholder="例如：个性化学习推荐"
        )
        
        search_type = st.radio(
            "搜索类型：",
            ["全部", "研究问题", "研究方法"],
            horizontal=True
        )
        
        if st.button("🔍 搜索") and search_query:
            with st.spinner("正在搜索..."):
                try:
                    if search_type == "全部":
                        results = st.session_state.retrieval_engine.search_all(search_query)
                        display_search_results(results.get('problems', []), "研究问题")
                        display_search_results(results.get('methods', []), "研究方法")
                    elif search_type == "研究问题":
                        results = st.session_state.retrieval_engine.search_problems(search_query)
                        display_search_results(results, "研究问题")
                    else:
                        results = st.session_state.retrieval_engine.search_methods(search_query)
                        display_search_results(results, "研究方法")
                        
                except Exception as e:
                    st.error(f"搜索时出错：{e}")
    
    with tab3:
        st.markdown('<h2 class="sub-header">数据管理</h2>', unsafe_allow_html=True)
        
        # 数据统计
        data_manager = DataManager()
        papers = data_manager.load_all_papers()
        
        if papers:
            st.markdown("### 📈 数据统计")
            
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("论文总数", len(papers))
            with col2:
                total_problems = sum(len(paper.research_problems) for paper in papers)
                st.metric("研究问题", total_problems)
            with col3:
                total_methods = sum(len(paper.research_methods) for paper in papers)
                st.metric("研究方法", total_methods)
            with col4:
                years = [paper.paper_info.year for paper in papers]
                avg_year = sum(years) / len(years) if years else 0
                st.metric("平均年份", f"{avg_year:.0f}")
            
            # 论文列表
            st.markdown("### 📄 论文列表")
            for paper in papers:
                with st.expander(f"{paper.paper_info.title} ({paper.paper_info.year})"):
                    st.markdown(f"**作者：** {', '.join(paper.paper_info.authors)}")
                    st.markdown(f"**发表于：** {paper.paper_info.venue}")
                    st.markdown(f"**摘要：** {paper.paper_info.abstract}")
                    st.markdown(f"**关键词：** {', '.join(paper.paper_info.keywords)}")
                    st.markdown(f"**研究问题数：** {len(paper.research_problems)}")
                    st.markdown(f"**研究方法数：** {len(paper.research_methods)}")
        else:
            st.info("暂无论文数据")
        
        # 重建索引功能
        st.markdown("---")
        st.markdown("### 🔄 系统维护")
        
        if st.button("重建语义空间", type="secondary"):
            with st.spinner("正在重建语义空间..."):
                try:
                    st.session_state.retrieval_engine.build_semantic_spaces()
                    st.success("语义空间重建完成！")
                except Exception as e:
                    st.error(f"重建失败：{e}")


if __name__ == "__main__":
    main()
