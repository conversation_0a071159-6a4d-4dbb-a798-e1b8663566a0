"""
语义空间构建模块
实现基于稠密向量（Qwen3嵌入）和稀疏向量（BM25）的语义空间构建
"""

import numpy as np
import pickle
import os
from typing import List, Dict, Tuple, Any, Optional
from sentence_transformers import SentenceTransformer
from rank_bm25 import BM25Okapi
import faiss
from tqdm import tqdm

from data_models import ExtractedPaperData, ResearchProblem, ResearchMethod, DataManager


class SemanticSpace:
    """语义空间基类"""
    
    def __init__(self, space_type: str, vector_db_dir: str = "./vector_db"):
        self.space_type = space_type  # 'problem' 或 'method'
        self.vector_db_dir = vector_db_dir
        self.items = []  # 存储所有项目（问题或方法）
        self.texts = []  # 存储对应的文本
        self.paper_ids = []  # 存储来源论文ID
        
        # 确保向量数据库目录存在
        os.makedirs(vector_db_dir, exist_ok=True)
    
    def add_items(self, papers: List[ExtractedPaperData]):
        """添加论文中的项目到语义空间"""
        for paper in papers:
            if self.space_type == 'problem':
                for problem in paper.research_problems:
                    self.items.append(problem)
                    # 组合标题和描述作为文本
                    text = f"{problem.title} {problem.description}"
                    self.texts.append(text)
                    self.paper_ids.append(paper.paper_info.paper_id)
            elif self.space_type == 'method':
                for method in paper.research_methods:
                    self.items.append(method)
                    # 组合名称、描述和技术作为文本
                    techniques_str = " ".join(method.techniques)
                    text = f"{method.name} {method.description} {techniques_str}"
                    self.texts.append(text)
                    self.paper_ids.append(paper.paper_info.paper_id)
    
    def save(self, filename: str):
        """保存语义空间到文件"""
        filepath = os.path.join(self.vector_db_dir, filename)
        with open(filepath, 'wb') as f:
            pickle.dump({
                'space_type': self.space_type,
                'items': self.items,
                'texts': self.texts,
                'paper_ids': self.paper_ids
            }, f)
    
    def load(self, filename: str) -> bool:
        """从文件加载语义空间"""
        filepath = os.path.join(self.vector_db_dir, filename)
        try:
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
                self.space_type = data['space_type']
                self.items = data['items']
                self.texts = data['texts']
                self.paper_ids = data['paper_ids']
            return True
        except FileNotFoundError:
            return False


class DenseSemanticSpace(SemanticSpace):
    """稠密语义空间（基于Qwen3嵌入模型）"""
    
    def __init__(self, space_type: str, model_name: str = "sentence-transformers/all-MiniLM-L6-v2", 
                 vector_db_dir: str = "./vector_db"):
        super().__init__(space_type, vector_db_dir)
        # 注意：这里使用了一个轻量级的替代模型，因为Qwen3可能需要特殊配置
        # 在实际部署时，可以替换为Qwen3的嵌入模型
        self.model = SentenceTransformer(model_name)
        self.index = None  # FAISS索引
        self.embeddings = None
    
    def build_index(self):
        """构建FAISS索引"""
        if not self.texts:
            print("没有文本数据，无法构建索引")
            return
        
        print(f"正在为{len(self.texts)}个{self.space_type}项目生成嵌入向量...")
        # 生成嵌入向量
        self.embeddings = self.model.encode(self.texts, show_progress_bar=True)
        
        # 构建FAISS索引
        dimension = self.embeddings.shape[1]
        self.index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
        
        # 标准化向量（用于余弦相似度）
        faiss.normalize_L2(self.embeddings)
        self.index.add(self.embeddings.astype('float32'))
        
        print(f"FAISS索引构建完成，维度: {dimension}")
    
    def search(self, query: str, top_k: int = 5) -> List[Tuple[Any, float, str]]:
        """在稠密空间中搜索"""
        if self.index is None:
            return []
        
        # 生成查询向量
        query_embedding = self.model.encode([query])
        faiss.normalize_L2(query_embedding)
        
        # 搜索
        scores, indices = self.index.search(query_embedding.astype('float32'), top_k)
        
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx < len(self.items):
                results.append((self.items[idx], float(score), self.paper_ids[idx]))
        
        return results
    
    def save_index(self, filename: str):
        """保存FAISS索引"""
        if self.index is not None:
            filepath = os.path.join(self.vector_db_dir, filename)
            faiss.write_index(self.index, filepath)
            
            # 保存嵌入向量
            embeddings_file = filepath.replace('.index', '_embeddings.npy')
            np.save(embeddings_file, self.embeddings)
    
    def load_index(self, filename: str) -> bool:
        """加载FAISS索引"""
        filepath = os.path.join(self.vector_db_dir, filename)
        try:
            self.index = faiss.read_index(filepath)
            
            # 加载嵌入向量
            embeddings_file = filepath.replace('.index', '_embeddings.npy')
            self.embeddings = np.load(embeddings_file)
            return True
        except:
            return False


class SparseSemanticSpace(SemanticSpace):
    """稀疏语义空间（基于BM25）"""
    
    def __init__(self, space_type: str, vector_db_dir: str = "./vector_db"):
        super().__init__(space_type, vector_db_dir)
        self.bm25 = None
        self.tokenized_texts = []
    
    def _tokenize(self, text: str) -> List[str]:
        """简单的中文分词（可以替换为更好的分词器）"""
        import re
        # 简单的中英文分词
        tokens = re.findall(r'[\w]+', text.lower())
        return tokens
    
    def build_index(self):
        """构建BM25索引"""
        if not self.texts:
            print("没有文本数据，无法构建索引")
            return
        
        print(f"正在为{len(self.texts)}个{self.space_type}项目构建BM25索引...")
        
        # 分词
        self.tokenized_texts = [self._tokenize(text) for text in tqdm(self.texts)]
        
        # 构建BM25索引
        self.bm25 = BM25Okapi(self.tokenized_texts)
        
        print("BM25索引构建完成")
    
    def search(self, query: str, top_k: int = 5) -> List[Tuple[Any, float, str]]:
        """在稀疏空间中搜索"""
        if self.bm25 is None:
            return []
        
        # 分词查询
        tokenized_query = self._tokenize(query)
        
        # 获取BM25分数
        scores = self.bm25.get_scores(tokenized_query)
        
        # 获取top_k结果
        top_indices = np.argsort(scores)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            if idx < len(self.items):
                results.append((self.items[idx], float(scores[idx]), self.paper_ids[idx]))
        
        return results
    
    def save_index(self, filename: str):
        """保存BM25索引"""
        if self.bm25 is not None:
            filepath = os.path.join(self.vector_db_dir, filename)
            with open(filepath, 'wb') as f:
                pickle.dump({
                    'bm25': self.bm25,
                    'tokenized_texts': self.tokenized_texts
                }, f)
    
    def load_index(self, filename: str) -> bool:
        """加载BM25索引"""
        filepath = os.path.join(self.vector_db_dir, filename)
        try:
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
                self.bm25 = data['bm25']
                self.tokenized_texts = data['tokenized_texts']
            return True
        except FileNotFoundError:
            return False


class SemanticSpaceManager:
    """语义空间管理器"""
    
    def __init__(self, vector_db_dir: str = "./vector_db"):
        self.vector_db_dir = vector_db_dir

        # 创建四个语义空间：问题和方法各有稠密和稀疏两种
        self.problem_dense = DenseSemanticSpace('problem',
                                               model_name="sentence-transformers/all-MiniLM-L6-v2",
                                               vector_db_dir=vector_db_dir)
        self.problem_sparse = SparseSemanticSpace('problem', vector_db_dir)
        self.method_dense = DenseSemanticSpace('method',
                                              model_name="sentence-transformers/all-MiniLM-L6-v2",
                                              vector_db_dir=vector_db_dir)
        self.method_sparse = SparseSemanticSpace('method', vector_db_dir)
    
    def build_all_spaces(self, papers: List[ExtractedPaperData]):
        """构建所有语义空间"""
        print("开始构建语义空间...")
        
        # 添加数据到各个空间
        self.problem_dense.add_items(papers)
        self.problem_sparse.add_items(papers)
        self.method_dense.add_items(papers)
        self.method_sparse.add_items(papers)
        
        # 构建索引
        print("构建问题稠密空间...")
        self.problem_dense.build_index()
        
        print("构建问题稀疏空间...")
        self.problem_sparse.build_index()
        
        print("构建方法稠密空间...")
        self.method_dense.build_index()
        
        print("构建方法稀疏空间...")
        self.method_sparse.build_index()
        
        print("所有语义空间构建完成！")
    
    def save_all(self):
        """保存所有语义空间"""
        self.problem_dense.save('problem_dense_data.pkl')
        self.problem_dense.save_index('problem_dense.index')
        
        self.problem_sparse.save('problem_sparse_data.pkl')
        self.problem_sparse.save_index('problem_sparse.pkl')
        
        self.method_dense.save('method_dense_data.pkl')
        self.method_dense.save_index('method_dense.index')
        
        self.method_sparse.save('method_sparse_data.pkl')
        self.method_sparse.save_index('method_sparse.pkl')
        
        print("所有语义空间已保存")
    
    def load_all(self) -> bool:
        """加载所有语义空间"""
        try:
            # 检查所有必要的文件是否存在
            required_files = [
                'problem_dense_data.pkl', 'problem_dense.index',
                'problem_sparse_data.pkl', 'problem_sparse.pkl',
                'method_dense_data.pkl', 'method_dense.index',
                'method_sparse_data.pkl', 'method_sparse.pkl'
            ]

            missing_files = []
            for filename in required_files:
                filepath = os.path.join(self.vector_db_dir, filename)
                if not os.path.exists(filepath):
                    missing_files.append(filename)

            if missing_files:
                print(f"缺少语义空间文件: {missing_files}")
                return False

            # 加载所有语义空间
            success = True
            success &= self.problem_dense.load('problem_dense_data.pkl')
            success &= self.problem_dense.load_index('problem_dense.index')

            success &= self.problem_sparse.load('problem_sparse_data.pkl')
            success &= self.problem_sparse.load_index('problem_sparse.pkl')

            success &= self.method_dense.load('method_dense_data.pkl')
            success &= self.method_dense.load_index('method_dense.index')

            success &= self.method_sparse.load('method_sparse_data.pkl')
            success &= self.method_sparse.load_index('method_sparse.pkl')

            if success:
                print("所有语义空间加载成功")
                return True
            else:
                print("部分语义空间加载失败")
                return False

        except Exception as e:
            print(f"加载语义空间失败: {e}")
            return False
