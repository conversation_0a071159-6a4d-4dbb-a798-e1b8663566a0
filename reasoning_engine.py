"""
推理模块
基于GPT-4o的迭代推理和假设生成模块
"""

import os
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import json
from openai import OpenAI

from data_models import SearchResult, ExtractedPaperData
from retrieval_engine import RetrievalEngine


@dataclass
class ReasoningConfig:
    """推理配置"""
    model: str = "gpt-4o"
    max_tokens: int = 2000
    temperature: float = 0.7
    max_iterations: int = 3  # 最大迭代次数
    min_confidence: float = 0.6  # 最小置信度


@dataclass
class HypothesisResult:
    """假设生成结果"""
    hypothesis: str  # 生成的假设
    reasoning: str  # 推理过程
    confidence: float  # 置信度
    supporting_evidence: List[SearchResult]  # 支持证据
    follow_up_questions: List[str]  # 后续问题
    iteration_count: int  # 迭代次数


class ReasoningEngine:
    """推理引擎"""
    
    def __init__(self, config: ReasoningConfig = None, retrieval_engine: RetrievalEngine = None):
        self.config = config or ReasoningConfig()
        self.retrieval_engine = retrieval_engine
        
        # 初始化OpenAI客户端
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量")
        
        self.client = OpenAI(api_key=api_key)
        
        # 系统提示词
        self.system_prompt = """
你是一个专业的学习分析领域研究助手，擅长基于现有文献提出新的研究假设和问题。

你的任务是：
1. 分析用户提供的研究想法和相关文献信息
2. 基于现有研究发现知识空白和研究机会
3. 提出有理有据的研究假设
4. 评估假设的可行性和创新性
5. 提出后续需要探索的问题

请确保你的回答：
- 基于提供的文献证据
- 逻辑清晰，推理严谨
- 具有创新性和可行性
- 指出研究的潜在价值和意义
"""
    
    def _format_search_results(self, results: List[SearchResult]) -> str:
        """格式化搜索结果为文本"""
        if not results:
            return "未找到相关文献信息。"
        
        formatted_text = "相关文献信息：\n\n"
        for i, result in enumerate(results, 1):
            formatted_text += f"{i}. 【{result.item_type.upper()}】{result.content}\n"
            formatted_text += f"   来源：{result.paper_title} (相关度: {result.score:.2f})\n\n"
        
        return formatted_text
    
    def _extract_follow_up_questions(self, response_text: str) -> List[str]:
        """从回复中提取后续问题"""
        questions = []
        lines = response_text.split('\n')
        
        for line in lines:
            line = line.strip()
            if ('?' in line or '？' in line) and ('后续' in line or '进一步' in line or '需要' in line):
                # 清理问题文本
                question = line.replace('- ', '').replace('* ', '').strip()
                if question:
                    questions.append(question)
        
        return questions[:3]  # 最多返回3个问题
    
    def _estimate_confidence(self, response_text: str, evidence_count: int) -> float:
        """估算置信度"""
        base_confidence = 0.5
        
        # 基于证据数量调整
        evidence_factor = min(evidence_count * 0.1, 0.3)
        
        # 基于回复质量调整（简单的关键词检测）
        quality_keywords = ['基于', '根据', '研究表明', '文献显示', '证据', '支持']
        quality_factor = sum(1 for keyword in quality_keywords if keyword in response_text) * 0.05
        
        confidence = base_confidence + evidence_factor + quality_factor
        return min(confidence, 1.0)
    
    def generate_hypothesis(self, user_idea: str, max_iterations: int = None) -> HypothesisResult:
        """生成研究假设"""
        max_iterations = max_iterations or self.config.max_iterations
        
        all_evidence = []
        conversation_history = []
        current_query = user_idea
        
        for iteration in range(max_iterations):
            print(f"开始第 {iteration + 1} 轮推理...")
            
            # 检索相关信息
            search_results = self.retrieval_engine.search_all(current_query)
            problems = search_results.get('problems', [])
            methods = search_results.get('methods', [])
            
            # 合并所有结果
            current_evidence = problems + methods
            all_evidence.extend(current_evidence)
            
            # 构建提示词
            evidence_text = self._format_search_results(current_evidence)
            
            if iteration == 0:
                # 第一轮：基础假设生成
                user_prompt = f"""
用户的研究想法：{user_idea}

{evidence_text}

请基于以上信息：
1. 分析现有研究的状况和空白
2. 提出一个具体的、可验证的研究假设
3. 解释这个假设的理论基础和创新点
4. 评估研究的可行性和潜在价值
5. 提出需要进一步探索的问题

请用中文回答，结构清晰。
"""
            else:
                # 后续轮：基于新信息完善假设
                user_prompt = f"""
基于新的文献信息，请完善之前的研究假设：

新的文献信息：
{evidence_text}

之前的分析：
{conversation_history[-1] if conversation_history else ''}

请：
1. 整合新信息，完善研究假设
2. 加强理论支撑
3. 识别新的研究机会
4. 提出更具体的研究问题

请用中文回答。
"""
            
            # 调用GPT-4o
            try:
                response = self.client.chat.completions.create(
                    model=self.config.model,
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    max_tokens=self.config.max_tokens,
                    temperature=self.config.temperature
                )
                
                response_text = response.choices[0].message.content
                conversation_history.append(response_text)
                
                # 提取后续问题作为下一轮的查询
                follow_up_questions = self._extract_follow_up_questions(response_text)
                
                if follow_up_questions and iteration < max_iterations - 1:
                    # 使用第一个后续问题作为下一轮的查询
                    current_query = follow_up_questions[0]
                    print(f"下一轮将探索：{current_query}")
                else:
                    # 没有后续问题或已达到最大迭代次数，结束迭代
                    break
                    
            except Exception as e:
                print(f"调用GPT-4o时出错：{e}")
                if conversation_history:
                    response_text = conversation_history[-1]
                    follow_up_questions = []
                else:
                    response_text = "抱歉，无法生成假设。请检查API配置。"
                    follow_up_questions = []
                break
        
        # 去重证据
        unique_evidence = []
        seen_ids = set()
        for evidence in all_evidence:
            if evidence.item_id not in seen_ids:
                unique_evidence.append(evidence)
                seen_ids.add(evidence.item_id)
        
        # 估算置信度
        confidence = self._estimate_confidence(response_text, len(unique_evidence))
        
        # 提取最终的后续问题
        final_follow_up = self._extract_follow_up_questions(response_text)
        
        return HypothesisResult(
            hypothesis=response_text,
            reasoning="基于多轮文献检索和推理生成",
            confidence=confidence,
            supporting_evidence=unique_evidence[:10],  # 最多保留10个证据
            follow_up_questions=final_follow_up,
            iteration_count=iteration + 1
        )
    
    def refine_hypothesis(self, original_hypothesis: str, user_feedback: str) -> HypothesisResult:
        """基于用户反馈完善假设"""
        # 基于反馈进行新的检索
        search_results = self.retrieval_engine.search_all(user_feedback)
        problems = search_results.get('problems', [])
        methods = search_results.get('methods', [])
        evidence = problems + methods
        
        evidence_text = self._format_search_results(evidence)
        
        user_prompt = f"""
原始假设：
{original_hypothesis}

用户反馈：
{user_feedback}

新的相关文献：
{evidence_text}

请基于用户反馈和新的文献信息，完善和调整研究假设。请：
1. 回应用户的具体反馈
2. 整合新的文献证据
3. 调整假设的表述和范围
4. 提出改进的研究方案

请用中文回答。
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            
            response_text = response.choices[0].message.content
            confidence = self._estimate_confidence(response_text, len(evidence))
            follow_up_questions = self._extract_follow_up_questions(response_text)
            
            return HypothesisResult(
                hypothesis=response_text,
                reasoning="基于用户反馈的假设完善",
                confidence=confidence,
                supporting_evidence=evidence[:10],
                follow_up_questions=follow_up_questions,
                iteration_count=1
            )
            
        except Exception as e:
            print(f"调用GPT-4o时出错：{e}")
            return HypothesisResult(
                hypothesis="抱歉，无法完善假设。请检查API配置。",
                reasoning="API调用失败",
                confidence=0.0,
                supporting_evidence=[],
                follow_up_questions=[],
                iteration_count=0
            )
    
    def explain_evidence(self, evidence: SearchResult) -> str:
        """解释单个证据的相关性"""
        user_prompt = f"""
请解释以下文献信息的研究价值和可能的应用场景：

类型：{evidence.item_type}
内容：{evidence.content}
来源论文：{evidence.paper_title}

请简要说明：
1. 这个研究的主要贡献
2. 可能的应用领域
3. 与其他研究的关联性
4. 潜在的扩展方向

请用中文回答，控制在200字以内。
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=300,
                temperature=0.5
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"无法解释该证据：{e}"
