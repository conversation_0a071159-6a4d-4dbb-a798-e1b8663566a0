# 论文假设生成系统

基于学习分析领域文献的智能研究假设生成工具，通过语义空间检索和大模型推理，帮助研究者发现新的研究机会和生成有理有据的研究假设。

## 🎯 项目背景

在学术研究中，问题的提出是研究的第一步，也是最关键的一步。本项目聚焦于研究问题的提出阶段，期望能够借助已有的文献信息，通过设计合理的信息集成融合方式，使得大语言模型能够利用好这些信息进行有理可依、有理有据的假设生成。

## 🚀 主要功能

### 1. 智能假设生成
- 基于用户输入的研究想法，自动检索相关文献
- 使用GPT-4o进行多轮迭代推理
- 生成有理有据的研究假设和问题

### 2. 双路检索系统
- **稠密检索**：基于Sentence Transformers的语义相似度检索
- **稀疏检索**：基于BM25的关键词匹配检索
- 智能融合两种检索结果，提高召回率和准确率

### 3. 语义空间构建
- **问题空间**：存储和检索研究问题
- **方法空间**：存储和检索研究方法
- 支持实时更新和扩展

### 4. 交互式界面
- 基于Streamlit的Web界面
- 支持假设生成、文献检索、数据管理
- 实时反馈和迭代优化

## 📁 项目结构

```
paper_hgen/
├── README.md                 # 项目说明文档
├── requirements.txt          # Python依赖包
├── .env.example             # 环境变量示例
├── initialize_system.py     # 系统初始化脚本
├── app.py                   # Streamlit主应用
├── data_models.py           # 数据模型定义
├── semantic_space.py        # 语义空间构建模块
├── retrieval_engine.py      # 检索匹配引擎
├── reasoning_engine.py      # 推理模块
├── create_sample_data.py    # 示例数据创建脚本
├── data/                    # 论文数据存储目录
│   ├── paper_LA001.json
│   ├── paper_LA002.json
│   └── paper_LA003.json
└── vector_db/               # 向量数据库存储目录
    ├── problem_dense.index
    ├── problem_sparse.pkl
    ├── method_dense.index
    └── method_sparse.pkl
```

## 🛠️ 安装和配置

### 1. 环境要求
- Python 3.8+
- OpenAI API密钥

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置您的OpenAI API密钥
OPENAI_API_KEY=your_openai_api_key_here
```

### 4. 初始化系统
```bash
python initialize_system.py
```

这个脚本会：
- 创建必要的目录结构
- 生成示例论文数据
- 构建语义空间索引
- 测试系统功能

## 🚀 使用方法

### 快速启动（推荐）
```bash
python quick_start.py
```
这个脚本会自动：
- 检查和安装依赖
- 运行核心功能测试
- 初始化系统
- 启动Web界面

### 手动启动

#### 1. 启动Web界面
```bash
streamlit run app.py
```

#### 2. 访问系统
在浏览器中打开显示的URL（通常是 http://localhost:8501）

### 3. 核心功能测试（可选）
如果只想测试核心功能而不安装所有依赖：
```bash
python test_core.py
```

### 4. 使用功能

#### 假设生成
1. 在"假设生成"标签页输入您的研究想法
2. 点击"生成假设"按钮
3. 系统将进行多轮检索和推理，生成研究假设
4. 查看支持证据和后续探索问题
5. 可以提供反馈进一步完善假设

#### 文献检索
1. 在"文献检索"标签页输入关键词
2. 选择搜索类型（全部/研究问题/研究方法）
3. 查看检索结果和详细信息

#### 数据管理
1. 在"数据管理"标签页查看数据统计
2. 浏览现有论文列表
3. 重建语义空间索引

## 📊 数据结构

### 论文信息结构
```python
@dataclass
class PaperInfo:
    paper_id: str          # 论文唯一标识
    title: str             # 论文标题
    authors: List[str]     # 作者列表
    year: int              # 发表年份
    venue: str             # 发表场所
    abstract: str          # 摘要
    keywords: List[str]    # 关键词
```

### 研究问题结构
```python
@dataclass
class ResearchProblem:
    problem_id: str        # 问题唯一标识
    title: str             # 问题标题
    description: str       # 问题描述
    keywords: List[str]    # 关键词
    domain: str            # 研究领域
    sub_domain: str        # 子领域
    research_type: str     # 研究类型
```

### 研究方法结构
```python
@dataclass
class ResearchMethod:
    method_id: str         # 方法唯一标识
    name: str              # 方法名称
    description: str       # 方法描述
    category: str          # 方法类别
    techniques: List[str]  # 具体技术手段
    data_types: List[str]  # 数据类型
    analysis_tools: List[str] # 分析工具
```

## 🔧 系统配置

### 检索配置
- `dense_weight`: 稠密检索权重（默认0.6）
- `sparse_weight`: 稀疏检索权重（默认0.4）
- `top_k_per_space`: 每个空间返回的结果数（默认10）
- `final_top_k`: 最终返回的结果数（默认5）

### 推理配置
- `model`: 使用的模型（默认gpt-4o）
- `max_tokens`: 最大生成长度（默认2000）
- `temperature`: 创新度（默认0.7）
- `max_iterations`: 最大迭代次数（默认3）

## 📈 扩展指南

### 添加新的论文数据
1. 参考 `create_sample_data.py` 中的数据格式
2. 创建新的 `ExtractedPaperData` 对象
3. 使用 `DataManager.save_paper_data()` 保存
4. 重建语义空间索引

### 自定义检索策略
1. 继承 `SemanticSpace` 基类
2. 实现自定义的 `build_index()` 和 `search()` 方法
3. 在 `SemanticSpaceManager` 中集成新的检索策略

### 扩展推理能力
1. 修改 `ReasoningEngine` 中的系统提示词
2. 添加新的推理策略和评估方法
3. 集成其他大语言模型

## 🐛 常见问题

### Q: 系统初始化失败
A: 请检查：
- OpenAI API密钥是否正确设置
- 网络连接是否正常
- Python依赖是否完整安装

### Q: 检索结果为空
A: 可能原因：
- 查询词与数据库内容不匹配
- 语义空间未正确构建
- 尝试重建索引

### Q: 假设生成质量不高
A: 建议：
- 调整推理配置参数
- 增加更多相关文献数据
- 优化查询词的表述

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**：本项目目前使用示例数据进行演示。在实际使用中，请替换为您自己的文献数据，并根据具体需求调整配置参数。
