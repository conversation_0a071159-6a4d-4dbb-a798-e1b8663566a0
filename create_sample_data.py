"""
创建示例数据
用于测试系统功能的示例论文数据
"""

from data_models import *
from datetime import datetime
import os


def create_sample_papers():
    """创建示例论文数据"""
    
    # 示例论文1：学习分析中的个性化推荐
    paper1 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="LA001",
            title="基于学习行为分析的个性化学习路径推荐系统",
            authors=["张三", "李四", "王五"],
            year=2023,
            venue="教育技术学报",
            abstract="本研究提出了一种基于学习行为数据挖掘的个性化学习路径推荐方法，通过分析学习者的点击流数据、作业完成情况和测试成绩，构建学习者画像，并使用协同过滤算法为学习者推荐最适合的学习路径。",
            keywords=["学习分析", "个性化推荐", "学习路径", "协同过滤", "学习者画像"],
            doi="10.1234/example.2023.001"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P001",
                title="如何基于学习行为数据实现个性化学习路径推荐",
                description="传统的学习路径设计往往采用一刀切的方式，无法满足不同学习者的个性化需求。如何利用学习分析技术，基于学习者的行为数据，为其推荐最适合的学习路径，是当前亟需解决的问题。",
                keywords=["个性化推荐", "学习路径", "学习行为分析"],
                domain="教育技术",
                sub_domain="学习分析",
                research_type="实证研究"
            ),
            ResearchProblem(
                problem_id="P002",
                title="学习者画像构建的有效性评估",
                description="在个性化推荐系统中，学习者画像的准确性直接影响推荐效果。如何评估基于多维度学习数据构建的学习者画像的有效性，是系统优化的关键问题。",
                keywords=["学习者画像", "有效性评估", "多维度数据"],
                domain="教育技术",
                sub_domain="学习分析",
                research_type="方法研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M001",
                name="协同过滤推荐算法",
                description="基于用户-项目评分矩阵，通过计算用户或项目之间的相似度来进行推荐",
                category="定量方法",
                techniques=["用户协同过滤", "项目协同过滤", "矩阵分解"],
                data_types=["点击流数据", "评分数据", "行为序列"],
                analysis_tools=["Python", "Scikit-learn", "Surprise"]
            ),
            ResearchMethod(
                method_id="M002",
                name="学习行为序列分析",
                description="通过分析学习者在学习平台上的行为序列，挖掘学习模式和偏好",
                category="定量方法",
                techniques=["序列模式挖掘", "马尔可夫链", "时间序列分析"],
                data_types=["时间戳数据", "行为日志", "学习轨迹"],
                analysis_tools=["R", "Python", "WEKA"]
            )
        ],
        full_text="这里是论文的完整文本内容...",
        extraction_date=datetime.now().isoformat()
    )
    
    # 示例论文2：在线学习效果评估
    paper2 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="LA002",
            title="基于多模态数据融合的在线学习效果评估研究",
            authors=["赵六", "钱七", "孙八"],
            year=2023,
            venue="计算机教育",
            abstract="本研究提出了一种融合文本、视频和交互数据的多模态学习效果评估方法，通过深度学习技术自动分析学习者的学习状态和效果，为教师提供及时的反馈。",
            keywords=["多模态数据", "学习效果评估", "深度学习", "在线学习"],
            doi="10.1234/example.2023.002"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P003",
                title="多模态数据在学习效果评估中的融合策略",
                description="在线学习环境中产生了大量的多模态数据，包括文本、视频、音频和交互数据。如何有效融合这些异构数据来准确评估学习效果，是一个具有挑战性的问题。",
                keywords=["多模态数据融合", "学习效果评估", "异构数据"],
                domain="教育技术",
                sub_domain="学习分析",
                research_type="技术研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M003",
                name="多模态深度学习",
                description="使用深度神经网络处理和融合多种模态的数据",
                category="定量方法",
                techniques=["卷积神经网络", "循环神经网络", "注意力机制", "多模态融合"],
                data_types=["文本数据", "视频数据", "音频数据", "交互数据"],
                analysis_tools=["TensorFlow", "PyTorch", "Keras"]
            ),
            ResearchMethod(
                method_id="M004",
                name="实时学习状态监测",
                description="通过实时分析学习者的行为和生理数据来监测学习状态",
                category="定量方法",
                techniques=["实时数据流处理", "异常检测", "状态转换分析"],
                data_types=["实时行为数据", "生理数据", "眼动数据"],
                analysis_tools=["Apache Kafka", "Apache Storm", "Python"]
            )
        ],
        full_text="这里是论文2的完整文本内容...",
        extraction_date=datetime.now().isoformat()
    )
    
    # 示例论文3：智能教学系统
    paper3 = ExtractedPaperData(
        paper_info=PaperInfo(
            paper_id="LA003",
            title="基于知识图谱的智能教学系统设计与实现",
            authors=["周九", "吴十"],
            year=2022,
            venue="教育信息化",
            abstract="本研究设计并实现了一个基于知识图谱的智能教学系统，通过构建领域知识图谱，实现了知识点的智能推荐和学习路径的自动生成。",
            keywords=["知识图谱", "智能教学", "知识推荐", "学习路径生成"],
            doi="10.1234/example.2022.003"
        ),
        research_problems=[
            ResearchProblem(
                problem_id="P004",
                title="领域知识图谱在教学中的应用效果",
                description="知识图谱作为一种新兴的知识表示方法，在教学领域的应用还处于探索阶段。如何评估基于知识图谱的教学系统的实际效果，是需要深入研究的问题。",
                keywords=["知识图谱", "教学应用", "效果评估"],
                domain="教育技术",
                sub_domain="智能教学",
                research_type="应用研究"
            )
        ],
        research_methods=[
            ResearchMethod(
                method_id="M005",
                name="知识图谱构建",
                description="从文本中抽取实体和关系，构建结构化的知识图谱",
                category="定量方法",
                techniques=["命名实体识别", "关系抽取", "知识融合", "图嵌入"],
                data_types=["文本数据", "结构化数据", "半结构化数据"],
                analysis_tools=["Neo4j", "spaCy", "Stanford NLP"]
            )
        ],
        full_text="这里是论文3的完整文本内容...",
        extraction_date=datetime.now().isoformat()
    )
    
    return [paper1, paper2, paper3]


def save_sample_data():
    """保存示例数据到文件"""
    # 确保数据目录存在
    os.makedirs("./data", exist_ok=True)
    
    # 创建数据管理器
    data_manager = DataManager("./data")
    
    # 创建并保存示例数据
    sample_papers = create_sample_papers()
    
    for paper in sample_papers:
        data_manager.save_paper_data(paper)
        print(f"已保存论文数据: {paper.paper_info.title}")
    
    print(f"共创建了 {len(sample_papers)} 篇示例论文数据")


if __name__ == "__main__":
    save_sample_data()
