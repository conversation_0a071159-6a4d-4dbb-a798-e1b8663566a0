"""
快速启动脚本
用于快速测试和演示系统功能
"""

import os
import sys
import subprocess


def check_dependencies():
    """检查依赖包是否安装"""
    # 包名映射：pip包名 -> 导入名
    package_mapping = {
        'streamlit': 'streamlit',
        'openai': 'openai',
        'numpy': 'numpy',
        'pandas': 'pandas',
        'scikit-learn': 'sklearn',
        'sentence-transformers': 'sentence_transformers',
        'faiss-cpu': 'faiss',
        'rank-bm25': 'rank_bm25',
        'tqdm': 'tqdm',
        'python-dotenv': 'dotenv'
    }

    missing_packages = []

    for pip_name, import_name in package_mapping.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(pip_name)

    return missing_packages


def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败：{e}")
        return False


def check_api_key():
    """检查API密钥"""
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️ 未设置OpenAI API密钥")
        print("请按以下步骤设置：")
        print("1. 复制 .env.example 为 .env")
        print("2. 在 .env 文件中设置您的OpenAI API密钥")
        print("3. OPENAI_API_KEY=your_api_key_here")
        return False
    
    print("✅ API密钥已设置")
    return True


def run_core_test():
    """运行核心功能测试"""
    print("🧪 运行核心功能测试...")
    try:
        subprocess.check_call([sys.executable, "test_core.py"])
        return True
    except subprocess.CalledProcessError:
        return False


def initialize_system():
    """初始化系统"""
    print("🚀 初始化系统...")
    try:
        subprocess.check_call([sys.executable, "initialize_system.py"])
        return True
    except subprocess.CalledProcessError:
        return False


def start_streamlit():
    """启动Streamlit应用"""
    print("🌐 启动Web界面...")
    try:
        subprocess.check_call([sys.executable, "-m", "streamlit", "run", "app.py"])
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败：{e}")
    except KeyboardInterrupt:
        print("\n👋 应用已停止")


def main():
    """主函数"""
    print("🚀 论文假设生成系统 - 快速启动")
    print("=" * 50)
    
    # 检查依赖
    missing_packages = check_dependencies()
    if missing_packages:
        print(f"❌ 缺少依赖包：{', '.join(missing_packages)}")
        
        install_choice = input("是否自动安装依赖包？(y/n): ").lower().strip()
        if install_choice == 'y':
            if not install_dependencies():
                print("❌ 依赖包安装失败，请手动运行：pip install -r requirements.txt")
                return
        else:
            print("请手动安装依赖包：pip install -r requirements.txt")
            return
    else:
        print("✅ 所有依赖包已安装")
    
    # 运行核心测试
    if not run_core_test():
        print("❌ 核心功能测试失败")
        return
    
    # 检查API密钥
    if not check_api_key():
        print("⚠️ 跳过完整系统初始化（需要API密钥）")
        print("您仍然可以：")
        print("1. 查看示例数据")
        print("2. 测试数据模型")
        print("3. 设置API密钥后运行完整功能")
        
        demo_choice = input("是否启动演示模式？(y/n): ").lower().strip()
        if demo_choice == 'y':
            print("🎯 启动演示模式...")
            # 这里可以添加演示模式的代码
            print("演示模式功能：")
            print("- 查看示例论文数据")
            print("- 了解数据结构")
            print("- 测试基础功能")
        return
    
    # 初始化系统
    if not initialize_system():
        print("❌ 系统初始化失败")
        return
    
    print("✅ 系统初始化完成")
    
    # 启动选择
    print("\n🎯 选择启动方式：")
    print("1. 启动Web界面 (推荐)")
    print("2. 命令行模式")
    print("3. 退出")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == '1':
        start_streamlit()
    elif choice == '2':
        print("💻 命令行模式")
        print("您可以导入模块并使用Python代码：")
        print("from data_models import *")
        print("from retrieval_engine import *")
        print("from reasoning_engine import *")
    elif choice == '3':
        print("👋 再见！")
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 发生错误：{e}")
        import traceback
        traceback.print_exc()
