
# 使用示例

from data_models import DataManager
from retrieval_engine import RetrievalEngine
from reasoning_engine import ReasoningEngine

# 1. 加载数据
data_manager = DataManager("./data")
papers = data_manager.load_all_papers()

# 2. 初始化检索引擎（需要安装依赖后使用）
# retrieval_engine = RetrievalEngine()
# retrieval_engine.build_semantic_spaces(papers)

# 3. 搜索相关文献（需要安装依赖后使用）
# results = retrieval_engine.search_all("个性化学习")

# 4. 生成研究假设（需要OpenAI API密钥）
# reasoning_engine = ReasoningEngine(retrieval_engine=retrieval_engine)
# hypothesis = reasoning_engine.generate_hypothesis("如何提高在线学习效果？")
