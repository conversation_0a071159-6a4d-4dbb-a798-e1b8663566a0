"""
核心功能测试脚本
测试数据模型和基础功能，不依赖外部包
"""

import json
import os
from data_models import *
from create_sample_data import create_sample_papers


def test_data_models():
    """测试数据模型"""
    print("🧪 测试数据模型...")
    
    # 创建示例数据
    papers = create_sample_papers()
    print(f"✅ 创建了 {len(papers)} 篇示例论文")
    
    # 测试数据管理器
    data_manager = DataManager("./data")
    
    # 保存数据
    for paper in papers:
        data_manager.save_paper_data(paper)
    print("✅ 数据保存成功")
    
    # 加载数据
    loaded_papers = data_manager.load_all_papers()
    print(f"✅ 加载了 {len(loaded_papers)} 篇论文")
    
    # 验证数据完整性
    total_problems = sum(len(paper.research_problems) for paper in loaded_papers)
    total_methods = sum(len(paper.research_methods) for paper in loaded_papers)
    print(f"✅ 总计：{total_problems} 个研究问题，{total_methods} 个研究方法")
    
    return loaded_papers


def test_search_results():
    """测试搜索结果数据结构"""
    print("\n🧪 测试搜索结果...")
    
    # 创建示例搜索结果
    result = SearchResult(
        item_id="P001",
        item_type="problem",
        content="测试研究问题",
        score=0.85,
        paper_id="LA001",
        paper_title="测试论文"
    )
    
    # 转换为字典
    result_dict = result.to_dict()
    print("✅ 搜索结果数据结构正常")
    print(f"   示例结果：{result_dict}")
    
    return result


def display_paper_info(papers):
    """显示论文信息"""
    print("\n📚 论文数据概览：")
    print("-" * 60)
    
    for i, paper in enumerate(papers, 1):
        print(f"\n{i}. {paper.paper_info.title}")
        print(f"   作者：{', '.join(paper.paper_info.authors)}")
        print(f"   年份：{paper.paper_info.year}")
        print(f"   研究问题数：{len(paper.research_problems)}")
        print(f"   研究方法数：{len(paper.research_methods)}")
        
        # 显示研究问题
        if paper.research_problems:
            print("   研究问题：")
            for j, problem in enumerate(paper.research_problems, 1):
                print(f"     {j}. {problem.title}")
        
        # 显示研究方法
        if paper.research_methods:
            print("   研究方法：")
            for j, method in enumerate(paper.research_methods, 1):
                print(f"     {j}. {method.name}")


def test_data_extraction():
    """测试数据提取和转换"""
    print("\n🧪 测试数据提取...")
    
    papers = create_sample_papers()
    
    # 提取所有研究问题
    all_problems = []
    all_methods = []
    
    for paper in papers:
        all_problems.extend(paper.research_problems)
        all_methods.extend(paper.research_methods)
    
    print(f"✅ 提取了 {len(all_problems)} 个研究问题")
    print(f"✅ 提取了 {len(all_methods)} 个研究方法")
    
    # 按领域分组
    domains = {}
    for problem in all_problems:
        domain = problem.domain
        if domain not in domains:
            domains[domain] = []
        domains[domain].append(problem)
    
    print(f"✅ 涉及 {len(domains)} 个研究领域：")
    for domain, problems in domains.items():
        print(f"   {domain}: {len(problems)} 个问题")
    
    return all_problems, all_methods


def create_usage_example():
    """创建使用示例"""
    print("\n📝 创建使用示例...")
    
    example_code = '''
# 使用示例

from data_models import DataManager
from retrieval_engine import RetrievalEngine
from reasoning_engine import ReasoningEngine

# 1. 加载数据
data_manager = DataManager("./data")
papers = data_manager.load_all_papers()

# 2. 初始化检索引擎（需要安装依赖后使用）
# retrieval_engine = RetrievalEngine()
# retrieval_engine.build_semantic_spaces(papers)

# 3. 搜索相关文献（需要安装依赖后使用）
# results = retrieval_engine.search_all("个性化学习")

# 4. 生成研究假设（需要OpenAI API密钥）
# reasoning_engine = ReasoningEngine(retrieval_engine=retrieval_engine)
# hypothesis = reasoning_engine.generate_hypothesis("如何提高在线学习效果？")
'''
    
    with open("usage_example.py", "w", encoding="utf-8") as f:
        f.write(example_code)
    
    print("✅ 使用示例已保存到 usage_example.py")


def main():
    """主测试函数"""
    print("🚀 开始核心功能测试...\n")
    
    try:
        # 测试数据模型
        papers = test_data_models()
        
        # 测试搜索结果
        test_search_results()
        
        # 显示论文信息
        display_paper_info(papers)
        
        # 测试数据提取
        test_data_extraction()
        
        # 创建使用示例
        create_usage_example()
        
        print("\n🎉 核心功能测试完成！")
        print("\n📋 下一步：")
        print("1. 安装依赖包：pip install -r requirements.txt")
        print("2. 设置OpenAI API密钥：复制.env.example为.env并填入密钥")
        print("3. 运行完整初始化：python initialize_system.py")
        print("4. 启动Web界面：streamlit run app.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 测试失败，请检查错误信息")
        exit(1)
    else:
        print("\n✅ 测试成功！")
