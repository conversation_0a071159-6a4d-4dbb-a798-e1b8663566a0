"""
调试检索功能
"""

from data_models import DataManager
from retrieval_engine import RetrievalEngine
import os


def debug_retrieval():
    """调试检索功能"""
    print("🔍 调试检索功能...")
    
    # 1. 检查数据
    print("\n1. 检查数据...")
    data_manager = DataManager("./data")
    papers = data_manager.load_all_papers()
    print(f"   加载了 {len(papers)} 篇论文")
    
    total_problems = sum(len(paper.research_problems) for paper in papers)
    total_methods = sum(len(paper.research_methods) for paper in papers)
    print(f"   总计：{total_problems} 个研究问题，{total_methods} 个研究方法")
    
    # 2. 检查向量数据库文件
    print("\n2. 检查向量数据库文件...")
    vector_db_files = os.listdir("./vector_db") if os.path.exists("./vector_db") else []
    print(f"   向量数据库文件：{vector_db_files}")
    
    # 3. 初始化检索引擎
    print("\n3. 初始化检索引擎...")
    try:
        retrieval_engine = RetrievalEngine()
        
        # 检查是否能初始化
        if retrieval_engine.initialize():
            print("   ✅ 检索引擎初始化成功")
        else:
            print("   ❌ 检索引擎初始化失败，重新构建...")
            retrieval_engine.build_semantic_spaces(papers)
            print("   ✅ 语义空间重新构建完成")
        
        # 4. 测试检索功能
        print("\n4. 测试检索功能...")
        
        # 检查语义空间中的数据
        print(f"   问题稠密空间项目数：{len(retrieval_engine.semantic_manager.problem_dense.items)}")
        print(f"   问题稀疏空间项目数：{len(retrieval_engine.semantic_manager.problem_sparse.items)}")
        print(f"   方法稠密空间项目数：{len(retrieval_engine.semantic_manager.method_dense.items)}")
        print(f"   方法稀疏空间项目数：{len(retrieval_engine.semantic_manager.method_sparse.items)}")
        
        # 测试搜索
        test_queries = ["个性化学习", "深度学习", "知识图谱"]
        
        for query in test_queries:
            print(f"\n   测试查询：'{query}'")
            
            # 搜索问题
            problem_results = retrieval_engine.search_problems(query)
            print(f"     问题搜索结果：{len(problem_results)} 个")
            for i, result in enumerate(problem_results[:2]):
                print(f"       {i+1}. {result.content[:50]}... (分数: {result.score:.3f})")
            
            # 搜索方法
            method_results = retrieval_engine.search_methods(query)
            print(f"     方法搜索结果：{len(method_results)} 个")
            for i, result in enumerate(method_results[:2]):
                print(f"       {i+1}. {result.content[:50]}... (分数: {result.score:.3f})")
        
        print("\n✅ 检索功能测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 检索引擎测试失败：{e}")
        import traceback
        traceback.print_exc()
        return False


def test_semantic_spaces():
    """测试语义空间构建"""
    print("\n🏗️ 测试语义空间构建...")
    
    try:
        from semantic_space import SemanticSpaceManager
        from data_models import DataManager
        
        # 加载数据
        data_manager = DataManager("./data")
        papers = data_manager.load_all_papers()
        
        # 创建语义空间管理器
        semantic_manager = SemanticSpaceManager("./vector_db")
        
        # 添加数据
        semantic_manager.problem_dense.add_items(papers)
        semantic_manager.problem_sparse.add_items(papers)
        semantic_manager.method_dense.add_items(papers)
        semantic_manager.method_sparse.add_items(papers)
        
        print(f"   问题稠密空间文本数：{len(semantic_manager.problem_dense.texts)}")
        print(f"   问题稀疏空间文本数：{len(semantic_manager.problem_sparse.texts)}")
        print(f"   方法稠密空间文本数：{len(semantic_manager.method_dense.texts)}")
        print(f"   方法稀疏空间文本数：{len(semantic_manager.method_sparse.texts)}")
        
        # 显示一些示例文本
        if semantic_manager.problem_dense.texts:
            print(f"   示例问题文本：{semantic_manager.problem_dense.texts[0][:100]}...")
        
        if semantic_manager.method_dense.texts:
            print(f"   示例方法文本：{semantic_manager.method_dense.texts[0][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 语义空间测试失败：{e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始调试检索功能...\n")
    
    # 测试语义空间
    if not test_semantic_spaces():
        return False
    
    # 调试检索功能
    if not debug_retrieval():
        return False
    
    print("\n🎉 调试完成！")
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 调试失败")
            exit(1)
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误：{e}")
        import traceback
        traceback.print_exc()
        exit(1)
