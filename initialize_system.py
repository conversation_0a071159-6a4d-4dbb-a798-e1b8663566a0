"""
系统初始化脚本
用于首次运行时构建语义空间和准备系统
"""

import os
from dotenv import load_dotenv

from data_models import DataManager
from retrieval_engine import RetrievalEngine
from create_sample_data import save_sample_data


def main():
    """主初始化函数"""
    print("🚀 开始初始化论文假设生成系统...")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查必要的环境变量
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 错误：请先设置OPENAI_API_KEY环境变量")
        print("   1. 复制 .env.example 为 .env")
        print("   2. 在 .env 文件中设置您的OpenAI API密钥")
        return False
    
    # 创建必要的目录
    print("📁 创建目录结构...")
    os.makedirs("./data", exist_ok=True)
    os.makedirs("./vector_db", exist_ok=True)
    
    # 检查是否已有数据
    data_manager = DataManager("./data")
    existing_papers = data_manager.load_all_papers()
    
    if not existing_papers:
        print("📚 创建示例数据...")
        save_sample_data()
        existing_papers = data_manager.load_all_papers()
    
    print(f"✅ 找到 {len(existing_papers)} 篇论文数据")
    
    # 初始化检索引擎
    print("🔍 初始化检索引擎...")
    retrieval_engine = RetrievalEngine()
    
    # 检查是否已有语义空间
    if not retrieval_engine.initialize():
        print("🏗️ 构建语义空间（这可能需要几分钟）...")
        try:
            retrieval_engine.build_semantic_spaces(existing_papers)
            print("✅ 语义空间构建完成")
        except Exception as e:
            print(f"❌ 语义空间构建失败：{e}")
            return False
    else:
        print("✅ 语义空间已存在")
    
    # 测试系统功能
    print("🧪 测试系统功能...")
    try:
        # 测试检索功能
        test_results = retrieval_engine.search_all("个性化学习")
        problems_count = len(test_results.get('problems', []))
        methods_count = len(test_results.get('methods', []))
        print(f"   检索测试：找到 {problems_count} 个问题，{methods_count} 个方法")
        
        if problems_count == 0 and methods_count == 0:
            print("⚠️ 警告：检索功能可能存在问题")
        else:
            print("✅ 检索功能正常")
        
    except Exception as e:
        print(f"❌ 系统测试失败：{e}")
        return False
    
    print("\n🎉 系统初始化完成！")
    print("\n📋 使用说明：")
    print("1. 运行 'streamlit run app.py' 启动Web界面")
    print("2. 在浏览器中访问显示的URL")
    print("3. 在'假设生成'标签页输入您的研究想法")
    print("4. 系统将基于文献数据生成研究假设")
    
    print("\n💡 提示：")
    print("- 可以在'文献检索'标签页测试检索功能")
    print("- 在'数据管理'标签页查看数据统计")
    print("- 如需添加更多论文数据，请参考 create_sample_data.py")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 初始化失败，请检查错误信息并重试")
        exit(1)
    else:
        print("\n✅ 初始化成功！")
